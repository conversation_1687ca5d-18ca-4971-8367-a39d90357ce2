<template>
  <div class="model-card" @click="handleDetail">
    <el-checkbox
      :model-value="selected"
      class="card-checkbox"
      @click.stop
      @change="(checked: boolean) => emit('select', checked)"
    />
    <div class="card-image-container">
      <img class="card-image" :src="model.coverImageFileUrl || DEFAULT_COVER_URL" />
      <!-- 视频播放按钮 -->
      <div v-if="model.modelVideoFileUrl" class="video-play-button" @click.stop="playVideo">
        <Icon icon="ep:video-play" :size="24" />
      </div>
    </div>
    <span class="card-title">{{ model.modelName }}</span>

    <!-- 视频播放弹窗 -->
    <el-dialog v-model="dialogVideo" title="视频" width="900px" append-to-body>
      <div v-if="dialogVideo && model.modelVideoFileUrl" class="video-container">
        <video
          :src="model.modelVideoFileUrl"
          controls
          playsinline
          preload="metadata"
          class="video-player"
        >
          您的浏览器不支持视频播放。
        </video>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import type { Model } from '../types'
import { DEFAULT_COVER_URL } from '../constants'

const props = defineProps<{
  model: Model
  selected: boolean
}>()

const emit = defineEmits<{
  (e: 'select', checked: boolean): void
}>()

const router = useRouter()
const dialogVideo = ref(false)

const handleDetail = () => {
  // TODO: 跳转到模型详情页
  // router.push({ name: 'ModelDetail', query: { id: props.model.id } })
}

const playVideo = () => {
  if (props.model.modelVideoFileUrl) {
    dialogVideo.value = true
  }
}
</script>

<style scoped>
.model-card {
  position: relative;
  width: 256px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  cursor: pointer;
  transition: all 0.2s ease;
}

.card-checkbox {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 2;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-checkbox :deep(.el-checkbox__label) {
  display: none;
}

.card-image-container {
  position: relative;
  width: 100%;
  height: 160px;
}

.card-image {
  width: 100%;
  height: 100%;
  border-radius: 4px;
  object-fit: cover;
  background-color: #f0f2f5;
}

.video-play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 48px;
  height: 48px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 1;
}

.video-play-button:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: translate(-50%, -50%) scale(1.1);
}

.card-title {
  font-size: 16px;
  font-weight: 500;
  line-height: 19px;
  color: #1f2329;
  white-space: pre;
  margin: 16px 0 12px;
}

.video-container {
  width: 100%;
  display: flex;
  justify-content: center;
}

.video-player {
  width: 100%;
  max-width: 800px;
  height: auto;
  border-radius: 8px;
}
</style>
